from decimal import Decimal

from django import forms
from django.conf import settings
from django.contrib import admin
from django.contrib.auth import get_user_model
from django.contrib.auth.admin import UserAdmin
from django.db.models import QuerySet
from django.http import HttpRequest
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import path, reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _

from accounts.models import (
    FeeGroup,
    FeeGroupTgConfig,
    Platform,
    Profile,
    ReservedUsername,
    UserPlatform,
)
from payments.exceptions.withdraw_service_exceptions import (
    WithdrawServiceError,
)
from payments.models.withdraw import GroupWithdraw
from payments.services.crypto_service import TronCryptoService
from payments.services.wallet_service import WalletService
from payments.services.withdraw_service import WithdrawService

User = get_user_model()


@admin.register(Platform)
class PlatformAdmin(admin.ModelAdmin):
    list_display = ("name",)
    search_fields = ("name",)


class UserPlatformInline(admin.TabularInline):
    model = UserPlatform
    extra = 1
    fields = ("platform", "url")
    autocomplete_fields = ["platform"]

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).select_related("profile__user")


@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ("user__email", "user__username")
    inlines = [UserPlatformInline]
    search_fields = ("user__username", "user__email")

    def get_queryset(self, request: HttpRequest) -> QuerySet:
        return super().get_queryset(request).select_related("user")


class ProfileInline(admin.StackedInline):
    model = Profile

    def has_add_permission(self, request: HttpRequest, obj: Profile) -> bool:
        return False

    def has_delete_permission(self, request: HttpRequest, obj: Profile = None) -> bool:
        return False


@admin.register(User)
class UserAdmin(UserAdmin):
    fieldsets = (
        (None, {"fields": ("username", "password")}),
        (_("Personal info"), {"fields": ("email",)}),
        (_("Fee"), {"fields": ("fee_group",)}),
        (
            _("Permissions"),
            {
                "fields": (
                    "is_active",
                    "is_staff",
                    "is_confirmed",
                    "is_superuser",
                    "groups",
                    "user_permissions",
                ),
            },
        ),
        (_("Important dates"), {"fields": ("last_login", "date_joined")}),
    )
    add_fieldsets = (
        (
            None,
            {
                "classes": ("wide",),
                "fields": (
                    "username",
                    "email",
                    "usable_password",
                    "password1",
                    "password2",
                ),
            },
        ),
    )
    list_display = ("username", "email", "date_joined", "last_login")
    list_filter = ("is_staff", "is_superuser", "is_active", "groups", "is_confirmed")
    search_fields = ("username", "email")
    ordering = ("-date_joined",)

    def get_inlines(self, request: HttpRequest, obj: User | None = None) -> list:
        if obj is not None:
            return [ProfileInline]
        return []


class FeeGroupTgConfigInline(admin.TabularInline):
    model = FeeGroupTgConfig
    extra = 0


class GroupWithdrawForm(forms.Form):
    address = forms.CharField(
        max_length=255,
        help_text=_("TRC20 address"),
    )
    amount = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_("Amount to withdraw"),
        disabled=True,
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields["amount"].initial = kwargs["initial"]["amount"]

    def clean_address(self, *args, **kwargs):
        address = self.cleaned_data["address"]

        if not address:
            return address

        crypto_service = TronCryptoService()

        if not crypto_service.validate_address(address):
            raise forms.ValidationError(_("Invalid address"))

        return address


@admin.register(FeeGroup)
class FeeGroupAdmin(admin.ModelAdmin):
    list_display = ("name", "sale_percent", "withdraw")
    search_fields = ("name",)
    inlines = (FeeGroupTgConfigInline,)
    readonly_fields = ("withdraw",)

    def withdraw(self, obj: FeeGroup):
        wallet_service = WalletService()
        balance = wallet_service.get_group_balance(obj)

        if obj.name == "All":
            return format_html(
                f'<p">{balance}$</p>'
            )

        if obj.group_withdraws.filter(
            status=GroupWithdraw.StatusChoices.PENDING
        ).exists():
            return format_html(
                '<a class="button" style="background-color: #f9d300; pointer-events: none; color: black;" href="#" disabled>Has pending withdraw</a>'
            )
        elif balance >= settings.WITHDRAW_MIN_AMOUNT:
            return format_html(
                f'<a class="button" style="background-color: green;" href="{reverse("admin:group-withdraw", args=[obj.pk])}?balance={balance}">{balance}$</a>'
            )
        else:
            return format_html(
                f'<a class="button" style="background-color: red; pointer-events: none;" href="#" disabled>{balance}$</a>'
            )

    def get_urls(self):
        custom_urls = [
            path(
                "group-withdraw/<uuid:pk>/",
                self.admin_site.admin_view(self.group_withdraw),
                name="group-withdraw",
            ),
        ]

        return custom_urls + super().get_urls()

    def group_withdraw(self, request: HttpRequest, pk: str):
        group = get_object_or_404(FeeGroup, pk=pk)
        balance = Decimal(request.GET.get("balance", 0))
        form = GroupWithdrawForm(request.POST or None, initial={"amount": balance})

        if form.is_valid():
            address = form.cleaned_data["address"]
            amount = form.cleaned_data["amount"]

            withdraw_service = WithdrawService()
            try:
                withdraw = withdraw_service.create_group_withdraw(
                    group, address, amount
                )

                self.message_user(request, "Withdraw created successfully.")

                return redirect(
                    reverse("admin:payments_groupwithdraw_change", args=[withdraw.pk])
                )

            except WithdrawServiceError as e:
                self.message_user(request, e, level="error")

            return redirect(reverse("admin:accounts_feegroup_changelist"))

        return render(
            request,
            "accounts/admin/group_withdraw.html",
            {
                "form": form,
                "group": group,
            },
        )


@admin.register(ReservedUsername)
class ReservedUsernameAdmin(admin.ModelAdmin):
    list_display = ("username",)
    search_fields = ("username",)
