from django.conf import settings
from django.db import models
from django.db.models import Q, UniqueConstraint
from django.utils.translation import gettext_lazy as _

from base.models import TimeStampedUUIDModel


class GroupWithdraw(TimeStampedUUIDModel):
    class StatusChoices(models.TextChoices):
        PENDING = "pending", "Pending"
        DONE = "done", "Done"

    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_("Amount of the withdraw."),
    )
    status = models.CharField(
        max_length=25,
        choices=StatusChoices.choices,
        help_text=_("Status of the withdraw."),
        default=StatusChoices.PENDING,
        db_index=True,
    )
    address = models.CharField(
        max_length=255,
        help_text=_("Crypto wallet address of the withdraw."),
    )
    fee_group = models.ForeignKey(
        "accounts.FeeGroup",
        on_delete=models.PROTECT,
        help_text=_("Fee group associated with the withdraw."),
        related_name="group_withdraws",
    )

    class Meta:
        ordering = ["-created_at"]
        constraints = [
            UniqueConstraint(
                fields=["fee_group"],
                condition=Q(status="pending"),
                name="unique_pending_group_withdraw_per_fee_group",
            )
        ]


class Withdraw(TimeStampedUUIDModel):
    class StatusChoices(models.TextChoices):
        PENDING = "pending", "Pending"
        DONE = "done", "Done"
        ERROR = "error", "Error"

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
        help_text=_("User associated with the withdraw."),
        related_name="withdraws",
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text=_("Amount of the withdraw."),
    )
    status = models.CharField(
        max_length=25,
        choices=StatusChoices.choices,
        help_text=_("Status of the withdraw."),
        default=StatusChoices.PENDING,
        db_index=True,
    )
    address = models.CharField(
        max_length=255,
        help_text=_("Crypto wallet address of the withdraw."),
    )
    transaction = models.OneToOneField(
        "payments.Transaction",
        on_delete=models.PROTECT,
        help_text=_("Transaction associated with the withdraw."),
        related_name="withdraw",
        null=True,
    )
    group_withdraw = models.ForeignKey(
        "payments.GroupWithdraw",
        on_delete=models.CASCADE,
        help_text=_("Group withdraw associated with the withdraw."),
        related_name="withdraws",
        null=True,
        blank=True,
    )

    class Meta:
        ordering = ["-created_at"]
        constraints = [
            UniqueConstraint(
                fields=["user"],
                condition=Q(status="pending"),
                name="unique_pending_withdraw_per_user",
            )
        ]
