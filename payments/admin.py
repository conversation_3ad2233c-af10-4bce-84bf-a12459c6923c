from django.contrib import admin
from django.http import HttpRequest
from django.shortcuts import get_object_or_404, redirect
from django.urls import path, reverse
from django.utils.html import format_html

from payments.models import FeeService, Payment, Transaction
from payments.models.withdraw import GroupWithdraw, Withdraw
from payments.services.withdraw_service import WithdrawService


@admin.register(Transaction)
class TransactionAdmin(admin.ModelAdmin):
    list_display = ("created_at", "user", "type", "amount")
    list_filter = ("type",)
    search_fields = ("user__username",)
    ordering = ("-created_at",)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user")

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(FeeService)
class FeeServiceAdmin(admin.ModelAdmin):
    pass


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = (
        "created_at",
        "status",
        "amount",
        "fee_service_amount",
        "fee_group_amount",
    )

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False


@admin.register(Withdraw)
class WithdrawAdmin(admin.ModelAdmin):
    list_display = ("created_at", "user", "address", "status", "amount")
    list_filter = ("status",)
    search_fields = ("user__username", "user__email", "address")

    def get_readonly_fields(self, request, obj=None):
        return [
            field.name for field in self.model._meta.fields if field.name != "status"
        ]

    def get_queryset(self, request):
        return super().get_queryset(request).select_related("user")

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return True

    def has_delete_permission(self, request, obj=None):
        return False


class WithdrawInline(admin.TabularInline):
    model = Withdraw
    extra = 0

    def has_add_permission(self, request: HttpRequest, obj: Withdraw = None) -> bool:
        return False

    def has_delete_permission(self, request: HttpRequest, obj: Withdraw = None) -> bool:
        return False

    def has_change_permission(self, request: HttpRequest, obj: Withdraw = None) -> bool:
        return False


@admin.register(GroupWithdraw)
class GroupWithdrawAdmin(admin.ModelAdmin):
    list_display = ("created_at", "fee_group", "address", "status", "amount", "action")
    list_filter = ("status",)
    search_fields = ("fee_group__name", "address")
    inlines = (WithdrawInline,)
    readonly_fields = ("action",)

    def action(self, obj: GroupWithdraw):
        if obj.status == GroupWithdraw.StatusChoices.PENDING:
            return format_html(
                f'<a class="button" style="background-color: green;" href="{reverse("admin:group-withdraw-done", args=[obj.pk])}">Mark as done</a>'
            )
        return ""

    def get_urls(self):
        custom_urls = [
            path(
                "group-withdraw-done/<uuid:pk>/",
                self.admin_site.admin_view(self.mark_as_done),
                name="group-withdraw-done",
            ),
        ]
        return custom_urls + super().get_urls()

    def mark_as_done(self, request: HttpRequest, pk: str):
        withdraw = get_object_or_404(GroupWithdraw, pk=pk)

        if withdraw.status != GroupWithdraw.StatusChoices.PENDING:
            self.message_user(request, "Withdraw is not pending.", level="error")

            return redirect(reverse("admin:payments_groupwithdraw_changelist"))

        withdraw_service = WithdrawService()
        withdraw_service.mark_group_withdraw_as_done(withdraw)

        self.message_user(request, "Withdraw marked as done.")

        return redirect(reverse("admin:payments_groupwithdraw_changelist"))

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        if obj is not None and obj.status == GroupWithdraw.StatusChoices.PENDING:
            return True
        return False
